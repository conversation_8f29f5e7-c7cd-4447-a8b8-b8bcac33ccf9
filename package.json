{"name": "twilio-business-messaging", "version": "1.0.0", "description": "Twilio Business-Initiated Messaging Backend API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "start:utf8": "chcp 65001 && node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:all": "node tests/run-all-tests.js", "test:media-processing": "node tests/media-processing/test-twilio-download.js", "test:unit": "jest tests/unit/*.test.js", "test:integration": "node tests/integration/test-whatsapp-media-integration.js", "test:media": "jest tests/unit/twilio-media-download.test.js", "test:coze-api": "node tests/coze-api/test-coze-v3.js", "test:whatsapp": "node tests/whatsapp/test-whatsapp-message.js", "test:tos-config": "node tests/media-processing/test-tos-config.js", "test:tos-upload": "node tests/media-processing/test-tos-upload-fix.js", "debug:tos": "node tests/media-processing/debug-tos-sdk.js", "test:cleanup": "node tests/cleanup-empty-tests.js", "test:cleanup:execute": "node tests/cleanup-empty-tests.js --execute", "test:zep": "npm test tests/unit/zep-cloud.test.js", "test:zep-connection": "node test-zep-connection.js", "test:volcengine-zep": "npm test tests/unit/zep-volcengine-integration.test.js", "demo:zep": "node zep-demo.js", "demo:volcengine-zep": "node volcengine-zep-demo.js", "demo:zep-knowledge": "node zep-knowledge-base-demo.js", "kb:create": "node zep-knowledge-manager.js create", "kb:import": "node zep-knowledge-manager.js import", "kb:search": "node zep-knowledge-manager.js search", "test:knowledge-base": "node test-knowledge-base.js", "init:knowledge": "node scripts/initializeKnowledgeGraph.js", "reset:knowledge": "node scripts/initializeKnowledgeGraph.js --reset", "test:intent": "node scripts/testIntentRecognition.js", "test:preferences": "node test-user-preferences.js", "test:unknown": "node test-unknown-info.js", "test:honesty": "node test-honesty-principle.js", "start:v2": "node src/app.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["twi<PERSON>", "messaging", "business-initiated", "webhook", "nodejs", "express"], "author": "Your Name", "license": "MIT", "dependencies": {"@getzep/zep-cloud": "^2.21.0", "@line/bot-sdk": "^10.0.0", "@slack/bolt": "^4.4.0", "@volcengine/tos-sdk": "^2.7.5", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "form-data": "^4.0.4", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^8.0.3", "morgan": "^1.10.1", "openai": "^5.10.1", "sqlite3": "^5.1.7", "twilio": "^4.19.0", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}